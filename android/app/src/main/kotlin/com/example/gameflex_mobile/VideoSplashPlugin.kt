package com.example.gameflex_mobile

import android.content.Context
import android.media.MediaPlayer
import android.net.Uri
import io.flutter.embedding.engine.plugins.FlutterPlugin
import io.flutter.plugin.common.MethodCall
import io.flutter.plugin.common.MethodChannel
import io.flutter.plugin.common.MethodChannel.MethodCallHandler
import io.flutter.plugin.common.MethodChannel.Result

class VideoSplashPlugin: FlutterPlugin, MethodCallHandler {
    private lateinit var channel: MethodChannel
    private lateinit var context: Context
    private var mediaPlayer: MediaPlayer? = null

    override fun onAttachedToEngine(flutterPluginBinding: FlutterPlugin.FlutterPluginBinding) {
        channel = MethodChannel(flutterPluginBinding.binaryMessenger, "gameflex/video_splash")
        channel.setMethodCallHandler(this)
        context = flutterPluginBinding.applicationContext
    }

    override fun onMethodCall(call: MethodCall, result: Result) {
        when (call.method) {
            "playVideo" -> {
                val videoPath = call.argument<String>("videoPath")
                val duration = call.argument<Int>("duration") ?: 3000
                
                if (videoPath != null) {
                    playVideo(videoPath, duration, result)
                } else {
                    result.error("INVALID_ARGUMENT", "Video path is required", null)
                }
            }
            else -> {
                result.notImplemented()
            }
        }
    }

    private fun playVideo(videoPath: String, duration: Int, result: Result) {
        try {
            // For now, just simulate video playback with a delay
            // In a full implementation, you would use VideoView or ExoPlayer
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                result.success("Video playback completed")
            }, duration.toLong())
            
        } catch (e: Exception) {
            result.error("PLAYBACK_ERROR", "Failed to play video: ${e.message}", null)
        }
    }

    override fun onDetachedFromEngine(binding: FlutterPlugin.FlutterPluginBinding) {
        channel.setMethodCallHandler(null)
        mediaPlayer?.release()
        mediaPlayer = null
    }
}
