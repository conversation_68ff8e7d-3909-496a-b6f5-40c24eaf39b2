import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

class VideoSplashScreen extends StatefulWidget {
  final VoidCallback onVideoComplete;

  const VideoSplashScreen({
    super.key,
    required this.onVideoComplete,
  });

  @override
  State<VideoSplashScreen> createState() => _VideoSplashScreenState();
}

class _VideoSplashScreenState extends State<VideoSplashScreen>
    with TickerProviderStateMixin {
  bool _hasVideoCompleted = false;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;
  static const platform = MethodChannel('gameflex/video_splash');

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _startSplashSequence();
  }

  void _initializeAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeInOut,
    ));
  }

  void _startSplashSequence() async {
    // Start fade animation
    _fadeController.forward();

    if (Platform.isAndroid || Platform.isIOS) {
      // Try to play video on mobile platforms
      await _playVideoSplash();
    } else {
      // Show static splash for desktop platforms
      await Future.delayed(const Duration(seconds: 3));
      _onVideoComplete();
    }
  }

  Future<void> _playVideoSplash() async {
    try {
      // Use platform channel to play video
      await platform.invokeMethod('playVideo', {
        'videoPath': 'assets/videos/splash/swords.mp4',
        'duration': 3000, // 3 seconds
      });
      _onVideoComplete();
    } catch (e) {
      debugPrint('Video playback failed: $e');
      // Fallback to static splash
      await Future.delayed(const Duration(seconds: 3));
      _onVideoComplete();
    }
  }

  void _onVideoComplete() {
    if (_hasVideoCompleted) return;

    _hasVideoCompleted = true;
    widget.onVideoComplete();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black, // Black background to blend with video
      body: Stack(
        children: [
          // Animated GameFlex logo splash (shown while video plays or as fallback)
          Center(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // GameFlex logo
                  Image.asset(
                    'assets/images/logos/gameflexBranding.png',
                    width: 200,
                    height: 200,
                    fit: BoxFit.contain,
                  ),
                  const SizedBox(height: 32),
                  // Loading indicator
                  const CircularProgressIndicator(
                    color: Colors.white,
                  ),
                ],
              ),
            ),
          ),

          // Tap to skip (optional - you can remove this if you don't want skip functionality)
          Positioned.fill(
            child: GestureDetector(
              onTap: () {
                if (!_hasVideoCompleted) {
                  _onVideoComplete();
                }
              },
              child: Container(
                color: Colors.transparent,
              ),
            ),
          ),

          // Skip button (optional)
          Positioned(
            top: MediaQuery.of(context).padding.top + 16,
            right: 16,
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: TextButton(
                onPressed: () {
                  if (!_hasVideoCompleted) {
                    _onVideoComplete();
                  }
                },
                child: const Text(
                  'Skip',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
